<template>
	<div class="invoice-order-selector">
		<m-form
			ref="formRef"
			:model="formModel"
			:label-col="{ span: 0 }"
			:wrapper-col="{ span: 24 }"
		>
			<div
				v-for="(item, index) in orderSelectList"
				:key="item.id"
				class="order-select-item"
			>
				<div class="order-select-row">
					<!-- 开票订单下拉框 -->
					<div class="order-select-wrapper">
						<m-form-item
							:name="['orderSelectList', index, 'selectedOrderNo']"
							:rules="[{ required: true, message: '请选择开票订单' }]"
							class="order-select-form-item"
						>
							<m-select
								v-model:value="item.selectedOrderNo"
								@change="(value, option) => onChangeOrder(value, option, index)"
								:options="formatHistoryOrderList"
								:field-names="{
									label: 'formatName',
									value: 'orderNo'
								}"
								placeholder="请选择开票订单"
								:get-popup-container="trigger => trigger.parentNode"
								class="order-select"
							/>
						</m-form-item>
					</div>

					<!-- 删除按钮 -->
					<div class="action-buttons">
						<m-button
							v-if="orderSelectList.length > 1"
							type="text"
							danger
							@click="removeOrderSelect(index)"
							class="remove-btn"
						>
							删除
						</m-button>
					</div>
				</div>
			</div>

			<!-- 添加按钮 -->
			<div class="add-button-wrapper">
				<m-button
					type="dashed"
					@click="addOrderSelect"
					class="add-btn"
				>
					<plus-outlined />
					添加
				</m-button>
			</div>
		</m-form>
	</div>
</template>

<script lang="ts" src="./index.ts"></script>
<style lang="less" src="./index.less" scoped></style>
